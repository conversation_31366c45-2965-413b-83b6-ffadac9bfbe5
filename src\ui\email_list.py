#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件列表组件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTableWidget, 
    QTableWidgetItem, QHeaderView, QLabel, QPushButton,
    QLineEdit, QComboBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont
from datetime import datetime


class EmailListWidget(QWidget):
    """邮件列表组件"""
    
    # 信号定义
    email_selected = pyqtSignal(dict)  # 邮件选中信号
    
    def __init__(self):
        """初始化邮件列表"""
        super().__init__()
        self.init_ui()
        self.load_sample_data()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # 创建搜索栏
        self.create_search_bar(layout)
        
        # 创建邮件表格
        self.create_email_table(layout)
    
    def create_search_bar(self, parent_layout):
        """创建搜索栏"""
        search_layout = QHBoxLayout()
        
        # 搜索输入框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("搜索邮件...")
        self.search_input.textChanged.connect(self.filter_emails)
        search_layout.addWidget(self.search_input)
        
        # 排序选择
        sort_label = QLabel("排序:")
        search_layout.addWidget(sort_label)
        
        self.sort_combo = QComboBox()
        self.sort_combo.addItems(["时间降序", "时间升序", "发件人", "主题"])
        self.sort_combo.currentTextChanged.connect(self.sort_emails)
        search_layout.addWidget(self.sort_combo)
        
        parent_layout.addLayout(search_layout)
    
    def create_email_table(self, parent_layout):
        """创建邮件表格"""
        self.email_table = QTableWidget()
        self.email_table.setColumnCount(4)
        self.email_table.setHorizontalHeaderLabels(["发件人", "主题", "时间", "大小"])
        
        # 设置表格属性
        self.email_table.setSelectionBehavior(QTableWidget.SelectionBehavior.SelectRows)
        self.email_table.setAlternatingRowColors(True)
        self.email_table.verticalHeader().setVisible(False)
        
        # 设置列宽
        header = self.email_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeMode.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeMode.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeMode.ResizeToContents)
        
        # 连接信号
        self.email_table.itemSelectionChanged.connect(self.on_email_selected)
        
        parent_layout.addWidget(self.email_table)
    
    def load_sample_data(self):
        """加载示例数据"""
        sample_emails = [
            {
                "sender": "张三 <<EMAIL>>",
                "subject": "项目进度报告",
                "time": "2024-01-15 14:30",
                "size": "2.5 KB",
                "content": "这是一封关于项目进度的邮件内容..."
            },
            {
                "sender": "李四 <<EMAIL>>",
                "subject": "会议邀请",
                "time": "2024-01-15 10:15",
                "size": "1.8 KB",
                "content": "邀请您参加明天的项目会议..."
            },
            {
                "sender": "王五 <<EMAIL>>",
                "subject": "文档审核",
                "time": "2024-01-14 16:45",
                "size": "3.2 KB",
                "content": "请审核附件中的文档..."
            }
        ]
        
        self.emails = sample_emails
        self.display_emails(sample_emails)
    
    def display_emails(self, emails):
        """显示邮件列表"""
        self.email_table.setRowCount(len(emails))
        
        for row, email in enumerate(emails):
            # 发件人
            sender_item = QTableWidgetItem(email["sender"])
            sender_item.setFlags(sender_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.email_table.setItem(row, 0, sender_item)
            
            # 主题
            subject_item = QTableWidgetItem(email["subject"])
            subject_item.setFlags(subject_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            # 设置粗体字体表示未读
            font = QFont()
            font.setBold(True)
            subject_item.setFont(font)
            self.email_table.setItem(row, 1, subject_item)
            
            # 时间
            time_item = QTableWidgetItem(email["time"])
            time_item.setFlags(time_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.email_table.setItem(row, 2, time_item)
            
            # 大小
            size_item = QTableWidgetItem(email["size"])
            size_item.setFlags(size_item.flags() & ~Qt.ItemFlag.ItemIsEditable)
            self.email_table.setItem(row, 3, size_item)
    
    def filter_emails(self, text):
        """过滤邮件"""
        if not text:
            self.display_emails(self.emails)
            return
        
        filtered_emails = []
        for email in self.emails:
            if (text.lower() in email["sender"].lower() or 
                text.lower() in email["subject"].lower()):
                filtered_emails.append(email)
        
        self.display_emails(filtered_emails)
    
    def sort_emails(self, sort_type):
        """排序邮件"""
        if sort_type == "时间降序":
            sorted_emails = sorted(self.emails, key=lambda x: x["time"], reverse=True)
        elif sort_type == "时间升序":
            sorted_emails = sorted(self.emails, key=lambda x: x["time"])
        elif sort_type == "发件人":
            sorted_emails = sorted(self.emails, key=lambda x: x["sender"])
        elif sort_type == "主题":
            sorted_emails = sorted(self.emails, key=lambda x: x["subject"])
        else:
            sorted_emails = self.emails
        
        self.display_emails(sorted_emails)
    
    def on_email_selected(self):
        """邮件选中事件"""
        current_row = self.email_table.currentRow()
        if current_row >= 0 and current_row < len(self.emails):
            email = self.emails[current_row]
            self.email_selected.emit(email)
    
    def refresh_emails(self):
        """刷新邮件列表"""
        # TODO: 实现从邮件服务器获取邮件
        pass
