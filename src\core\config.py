#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序配置管理
"""

import os
import json
from pathlib import Path
from typing import Dict, Any


class AppConfig:
    """应用程序配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        self.config_dir = Path.home() / ".emailtool"
        self.config_file = self.config_dir / "config.json"
        self.accounts_file = self.config_dir / "accounts.json"
        
        # 确保配置目录存在
        self.config_dir.mkdir(exist_ok=True)
        
        # 默认配置
        self.default_config = {
            "theme": "light",  # light/dark
            "language": "zh_CN",
            "window": {
                "width": 1200,
                "height": 800,
                "maximized": False
            },
            "email": {
                "check_interval": 300,  # 秒
                "auto_check": True,
                "show_notifications": True
            }
        }
        
        # 加载配置
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # 合并默认配置
                return {**self.default_config, **config}
            except (json.JSONDecodeError, IOError):
                pass
        
        return self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=2, ensure_ascii=False)
        except IOError:
            pass
    
    def get(self, key: str, default=None):
        """获取配置值"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """设置配置值"""
        keys = key.split('.')
        config = self.config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self.save_config()
    
    def get_accounts_file(self) -> Path:
        """获取账户文件路径"""
        return self.accounts_file
