#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
    QSplitter, QMenuBar, QMenu, QStatusBar, QToolBar,
    QListWidget, QListWidgetItem, QStackedWidget,
    QLabel, QPushButton, QMessageBox
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QAction, QIcon

from ui.styles import AppStyles
from ui.email_list import EmailListWidget
from ui.email_viewer import EmailViewerWidget
from ui.account_dialog import AccountDialog
from core.config import AppConfig
from core.email_account import EmailAccountManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化配置和账户管理
        self.config = AppConfig()
        self.account_manager = EmailAccountManager(self.config.get_accounts_file())
        
        # 设置窗口属性
        self.setWindowTitle("邮箱工具")
        self.setMinimumSize(1000, 700)
        
        # 恢复窗口大小和位置
        self.restore_window_state()
        
        # 初始化UI
        self.init_ui()
        self.apply_theme()
        
        # 连接信号
        self.connect_signals()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建中央部件
        self.create_central_widget()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 添加账户
        add_account_action = QAction("添加账户(&A)", self)
        add_account_action.setShortcut("Ctrl+N")
        add_account_action.triggered.connect(self.add_account)
        file_menu.addAction(add_account_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 主题切换
        theme_action = QAction("切换主题(&T)", self)
        theme_action.triggered.connect(self.toggle_theme)
        view_menu.addAction(theme_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 添加账户按钮
        add_account_btn = QPushButton("添加账户")
        add_account_btn.clicked.connect(self.add_account)
        toolbar.addWidget(add_account_btn)
        
        toolbar.addSeparator()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_emails)
        toolbar.addWidget(refresh_btn)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧边栏
        self.create_sidebar(splitter)
        
        # 右侧内容区域
        self.create_content_area(splitter)
        
        # 设置分割器比例
        splitter.setSizes([250, 750])
    
    def create_sidebar(self, parent):
        """创建左侧边栏"""
        self.sidebar = QListWidget()
        self.sidebar.setMaximumWidth(300)
        self.sidebar.setMinimumWidth(200)
        
        # 添加默认项目
        folders = ["收件箱", "发件箱", "草稿箱", "已发送", "垃圾箱"]
        for folder in folders:
            item = QListWidgetItem(folder)
            self.sidebar.addItem(item)
        
        # 设置默认选中
        self.sidebar.setCurrentRow(0)
        
        parent.addWidget(self.sidebar)
    
    def create_content_area(self, parent):
        """创建右侧内容区域"""
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(0, 0, 0, 0)

        # 创建内容分割器
        content_splitter = QSplitter(Qt.Orientation.Vertical)
        content_layout.addWidget(content_splitter)

        # 邮件列表
        self.email_list = EmailListWidget()
        content_splitter.addWidget(self.email_list)

        # 邮件查看器
        self.email_viewer = EmailViewerWidget()
        content_splitter.addWidget(self.email_viewer)

        # 设置分割器比例
        content_splitter.setSizes([400, 300])

        parent.addWidget(content_widget)
    
    def apply_theme(self):
        """应用主题"""
        dark_mode = self.config.get("theme") == "dark"
        
        # 应用样式
        style = (
            AppStyles.get_main_window_style(dark_mode) +
            AppStyles.get_sidebar_style(dark_mode) +
            AppStyles.get_button_style(dark_mode)
        )
        
        self.setStyleSheet(style)
    
    def connect_signals(self):
        """连接信号"""
        # 侧边栏选择变化
        self.sidebar.currentRowChanged.connect(self.on_folder_changed)

        # 邮件列表选择变化
        self.email_list.email_selected.connect(self.email_viewer.display_email)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        width = self.config.get("window.width", 1200)
        height = self.config.get("window.height", 800)
        maximized = self.config.get("window.maximized", False)
        
        self.resize(width, height)
        
        if maximized:
            self.showMaximized()
    
    def save_window_state(self):
        """保存窗口状态"""
        if not self.isMaximized():
            self.config.set("window.width", self.width())
            self.config.set("window.height", self.height())
        
        self.config.set("window.maximized", self.isMaximized())
    
    def add_account(self):
        """添加邮箱账户"""
        dialog = AccountDialog(self)
        if dialog.exec() == AccountDialog.DialogCode.Accepted:
            account, password = dialog.get_account_data()
            if self.account_manager.add_account(account, password):
                self.status_bar.showMessage("账户添加成功", 3000)
                self.refresh_emails()
            else:
                QMessageBox.warning(self, "错误", "账户添加失败，可能邮箱已存在")
    
    def refresh_emails(self):
        """刷新邮件"""
        self.status_bar.showMessage("正在刷新邮件...")
        # TODO: 实现邮件刷新逻辑
        self.status_bar.showMessage("邮件刷新完成", 3000)
    
    def toggle_theme(self):
        """切换主题"""
        current_theme = self.config.get("theme", "light")
        new_theme = "dark" if current_theme == "light" else "light"
        self.config.set("theme", new_theme)
        self.apply_theme()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于邮箱工具",
            "邮箱工具 v1.0.0\n\n"
            "一个现代化的邮箱客户端应用程序\n"
            "使用PyQt6开发"
        )
    
    def on_folder_changed(self, index):
        """文件夹选择变化"""
        if index >= 0:
            folder_name = self.sidebar.item(index).text()
            self.status_bar.showMessage(f"当前文件夹: {folder_name}")
            # TODO: 加载对应文件夹的邮件
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_window_state()
        event.accept()
