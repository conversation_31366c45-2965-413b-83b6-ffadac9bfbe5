#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主窗口界面
"""

from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QHBoxLayout, QVBoxLayout, 
    QSplitter, QMenuBar, QMenu, QStatusBar, QToolBar,
    QListWidget, QListWidgetItem, QStackedWidget,
    QLabel, QPushButton, QMessageBox
)
from PyQt6.QtCore import Qt, QSize
from PyQt6.QtGui import QAction, QIcon

from ui.styles import AppStyles
from ui.account_list import AccountListWidget
from ui.latest_email_viewer import LatestEmailViewerWidget
from ui.account_dialog import AccountDialog
from core.config import AppConfig
from core.email_account import EmailAccountManager


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        """初始化主窗口"""
        super().__init__()
        
        # 初始化配置和账户管理
        self.config = AppConfig()
        self.account_manager = EmailAccountManager(self.config.get_accounts_file())
        
        # 设置窗口属性
        self.setWindowTitle("邮箱工具")
        self.setMinimumSize(1000, 700)
        
        # 恢复窗口大小和位置
        self.restore_window_state()
        
        # 初始化UI
        self.init_ui()
        self.apply_theme()
        
        # 连接信号
        self.connect_signals()

        # 加载账号列表
        self.load_accounts()
    
    def init_ui(self):
        """初始化用户界面"""
        # 创建菜单栏
        self.create_menu_bar()
        
        # 创建工具栏
        self.create_toolbar()
        
        # 创建状态栏
        self.create_status_bar()
        
        # 创建中央部件
        self.create_central_widget()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 添加账户
        add_account_action = QAction("添加账户(&A)", self)
        add_account_action.setShortcut("Ctrl+N")
        add_account_action.triggered.connect(self.add_account)
        file_menu.addAction(add_account_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出(&X)", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 视图菜单
        view_menu = menubar.addMenu("视图(&V)")
        
        # 主题切换
        theme_action = QAction("切换主题(&T)", self)
        theme_action.triggered.connect(self.toggle_theme)
        view_menu.addAction(theme_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")
        
        # 关于
        about_action = QAction("关于(&A)", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def create_toolbar(self):
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 添加账户按钮
        add_account_btn = QPushButton("添加账户")
        add_account_btn.clicked.connect(self.add_account)
        toolbar.addWidget(add_account_btn)
        
        toolbar.addSeparator()
        
        # 刷新按钮
        refresh_btn = QPushButton("刷新")
        refresh_btn.clicked.connect(self.refresh_current_account)
        toolbar.addWidget(refresh_btn)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("就绪")
    
    def create_central_widget(self):
        """创建中央部件"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建分割器
        splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧边栏
        self.create_sidebar(splitter)
        
        # 右侧内容区域
        self.create_content_area(splitter)
        
        # 设置分割器比例 (左侧账号列表约25%，右侧邮件内容约75%)
        splitter.setSizes([300, 900])

    def create_sidebar(self, parent):
        """创建左侧账号列表"""
        self.account_list = AccountListWidget()
        self.account_list.setMaximumWidth(400)
        self.account_list.setMinimumWidth(250)

        parent.addWidget(self.account_list)

    def create_content_area(self, parent):
        """创建右侧邮件内容区域"""
        # 最新邮件查看器
        self.latest_email_viewer = LatestEmailViewerWidget()
        parent.addWidget(self.latest_email_viewer)
    
    def apply_theme(self):
        """应用主题"""
        dark_mode = self.config.get("theme") == "dark"

        # 应用样式
        style = (
            AppStyles.get_main_window_style(dark_mode) +
            AppStyles.get_sidebar_style(dark_mode) +
            AppStyles.get_button_style(dark_mode) +
            AppStyles.get_account_list_style(dark_mode) +
            AppStyles.get_email_viewer_style(dark_mode)
        )

        self.setStyleSheet(style)
    
    def connect_signals(self):
        """连接信号"""
        # 账号列表信号
        self.account_list.account_selected.connect(self.on_account_selected)
        self.account_list.add_account_requested.connect(self.add_account)

        # 最新邮件查看器信号
        self.latest_email_viewer.refresh_requested.connect(self.refresh_account_emails)
    
    def restore_window_state(self):
        """恢复窗口状态"""
        width = self.config.get("window.width", 1200)
        height = self.config.get("window.height", 800)
        maximized = self.config.get("window.maximized", False)
        
        self.resize(width, height)
        
        if maximized:
            self.showMaximized()
    
    def save_window_state(self):
        """保存窗口状态"""
        if not self.isMaximized():
            self.config.set("window.width", self.width())
            self.config.set("window.height", self.height())
        
        self.config.set("window.maximized", self.isMaximized())
    
    def load_accounts(self):
        """加载账号列表"""
        accounts = self.account_manager.get_all_accounts()
        self.account_list.load_accounts(accounts)

    def add_account(self):
        """添加邮箱账户"""
        dialog = AccountDialog(self)
        if dialog.exec() == AccountDialog.DialogCode.Accepted:
            account, password = dialog.get_account_data()
            if self.account_manager.add_account(account, password):
                self.status_bar.showMessage("账户添加成功", 3000)
                self.load_accounts()  # 重新加载账号列表
                # 如果这是第一个账号，自动选中它
                if len(self.account_manager.get_all_accounts()) == 1:
                    self.account_list.account_list.setCurrentRow(0)
            else:
                QMessageBox.warning(self, "错误", "账户添加失败，可能邮箱已存在")

    def on_account_selected(self, account):
        """账号选择变化处理"""
        self.status_bar.showMessage(f"当前账号: {account.name} ({account.email})")

        # 显示该账号的最新邮件
        # TODO: 这里应该从邮件管理器获取实际的最新邮件
        # 现在先显示示例邮件
        sample_email = self.get_sample_email_for_account(account)
        self.latest_email_viewer.display_email(account, sample_email)

    def get_sample_email_for_account(self, account):
        """为指定账号获取示例邮件（临时方法）"""
        # TODO: 替换为实际的邮件获取逻辑
        return {
            "subject": f"欢迎使用 {account.name}",
            "sender": "<EMAIL>",
            "recipient": account.email,
            "time": "2024-01-15 10:30:00",
            "content": f"您好！\n\n欢迎使用邮箱工具！这是您账号 {account.name} 的示例邮件。\n\n当您连接到真实的邮箱服务器后，这里将显示您的最新邮件内容。\n\n祝您使用愉快！\n\n邮箱工具团队"
        }

    def refresh_account_emails(self, account):
        """刷新指定账号的邮件"""
        self.status_bar.showMessage(f"正在刷新 {account.name} 的邮件...")
        # TODO: 实现实际的邮件刷新逻辑
        # 现在先更新示例邮件
        sample_email = self.get_sample_email_for_account(account)
        self.latest_email_viewer.display_email(account, sample_email)
        self.status_bar.showMessage("邮件刷新完成", 3000)

    def refresh_current_account(self):
        """刷新当前选中的账号"""
        current_account = self.account_list.get_current_account()
        if current_account:
            self.refresh_account_emails(current_account)
        else:
            self.status_bar.showMessage("请先选择一个邮箱账号", 3000)
    
    def toggle_theme(self):
        """切换主题"""
        current_theme = self.config.get("theme", "light")
        new_theme = "dark" if current_theme == "light" else "light"
        self.config.set("theme", new_theme)
        self.apply_theme()
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于邮箱工具",
            "邮箱工具 v1.0.0\n\n"
            "一个现代化的邮箱客户端应用程序\n"
            "使用PyQt6开发"
        )
    

    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self.save_window_state()
        event.accept()
