#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件客户端核心功能
处理IMAP和SMTP协议通信
"""

import imaplib
import smtplib
import email
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.header import decode_header
from typing import List, Dict, Optional, Tuple
import ssl
from datetime import datetime

from core.email_account import EmailAccount


class EmailClient:
    """邮件客户端类"""
    
    def __init__(self, account: EmailAccount, password: str):
        """初始化邮件客户端"""
        self.account = account
        self.password = password
        self.imap_client = None
        self.smtp_client = None
    
    def connect_imap(self) -> bool:
        """连接IMAP服务器"""
        try:
            if self.account.use_ssl:
                self.imap_client = imaplib.IMAP4_SSL(
                    self.account.imap_server, 
                    self.account.imap_port
                )
            else:
                self.imap_client = imaplib.IMAP4(
                    self.account.imap_server, 
                    self.account.imap_port
                )
            
            # 登录
            self.imap_client.login(self.account.email, self.password)
            return True
            
        except Exception as e:
            print(f"IMAP连接失败: {e}")
            return False
    
    def connect_smtp(self) -> bool:
        """连接SMTP服务器"""
        try:
            if self.account.use_ssl:
                self.smtp_client = smtplib.SMTP_SSL(
                    self.account.smtp_server, 
                    self.account.smtp_port
                )
            else:
                self.smtp_client = smtplib.SMTP(
                    self.account.smtp_server, 
                    self.account.smtp_port
                )
                self.smtp_client.starttls()
            
            # 登录
            self.smtp_client.login(self.account.email, self.password)
            return True
            
        except Exception as e:
            print(f"SMTP连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.imap_client:
            try:
                self.imap_client.close()
                self.imap_client.logout()
            except:
                pass
            self.imap_client = None
        
        if self.smtp_client:
            try:
                self.smtp_client.quit()
            except:
                pass
            self.smtp_client = None
    
    def get_folders(self) -> List[str]:
        """获取邮箱文件夹列表"""
        if not self.imap_client:
            if not self.connect_imap():
                return []
        
        try:
            status, folders = self.imap_client.list()
            if status == 'OK':
                folder_list = []
                for folder in folders:
                    # 解析文件夹名称
                    folder_name = folder.decode().split('"')[-2]
                    folder_list.append(folder_name)
                return folder_list
        except Exception as e:
            print(f"获取文件夹失败: {e}")
        
        return []
    
    def get_emails(self, folder: str = 'INBOX', limit: int = 50) -> List[Dict]:
        """获取邮件列表"""
        if not self.imap_client:
            if not self.connect_imap():
                return []
        
        try:
            # 选择文件夹
            self.imap_client.select(folder)
            
            # 搜索邮件
            status, messages = self.imap_client.search(None, 'ALL')
            if status != 'OK':
                return []
            
            # 获取邮件ID列表
            email_ids = messages[0].split()
            
            # 限制数量并倒序（最新的在前）
            email_ids = email_ids[-limit:][::-1]
            
            emails = []
            for email_id in email_ids:
                email_data = self.get_email_by_id(email_id)
                if email_data:
                    emails.append(email_data)
            
            return emails
            
        except Exception as e:
            print(f"获取邮件失败: {e}")
            return []
    
    def get_email_by_id(self, email_id: bytes) -> Optional[Dict]:
        """根据ID获取邮件详情"""
        try:
            # 获取邮件数据
            status, msg_data = self.imap_client.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return None
            
            # 解析邮件
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # 提取邮件信息
            subject = self.decode_header_value(email_message['Subject'])
            sender = self.decode_header_value(email_message['From'])
            recipient = self.decode_header_value(email_message['To'])
            date = email_message['Date']
            
            # 获取邮件内容
            content = self.extract_email_content(email_message)
            
            return {
                'id': email_id.decode(),
                'subject': subject or '无主题',
                'sender': sender or '未知发件人',
                'recipient': recipient or '未知收件人',
                'time': self.parse_date(date),
                'content': content,
                'size': f"{len(msg_data[0][1]) / 1024:.1f} KB"
            }
            
        except Exception as e:
            print(f"解析邮件失败: {e}")
            return None
    
    def decode_header_value(self, value: str) -> str:
        """解码邮件头部值"""
        if not value:
            return ""
        
        try:
            decoded_parts = decode_header(value)
            result = ""
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        result += part.decode(encoding)
                    else:
                        result += part.decode('utf-8', errors='ignore')
                else:
                    result += part
            return result
        except:
            return value
    
    def extract_email_content(self, email_message) -> str:
        """提取邮件内容"""
        content = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                if content_type == "text/plain":
                    charset = part.get_content_charset() or 'utf-8'
                    try:
                        content = part.get_payload(decode=True).decode(charset)
                        break
                    except:
                        continue
        else:
            charset = email_message.get_content_charset() or 'utf-8'
            try:
                content = email_message.get_payload(decode=True).decode(charset)
            except:
                content = "无法解析邮件内容"
        
        return content
    
    def parse_date(self, date_str: str) -> str:
        """解析邮件日期"""
        if not date_str:
            return "未知时间"
        
        try:
            # 简单的日期解析，实际应用中可能需要更复杂的处理
            return date_str[:25]  # 截取前25个字符
        except:
            return "未知时间"
    
    def send_email(self, to: str, subject: str, content: str, 
                   cc: str = None, bcc: str = None) -> bool:
        """发送邮件"""
        if not self.smtp_client:
            if not self.connect_smtp():
                return False
        
        try:
            # 创建邮件消息
            msg = MIMEMultipart()
            msg['From'] = self.account.email
            msg['To'] = to
            msg['Subject'] = subject
            
            if cc:
                msg['Cc'] = cc
            
            # 添加邮件内容
            msg.attach(MIMEText(content, 'plain', 'utf-8'))
            
            # 发送邮件
            recipients = [to]
            if cc:
                recipients.extend(cc.split(','))
            if bcc:
                recipients.extend(bcc.split(','))
            
            self.smtp_client.send_message(msg, to_addrs=recipients)
            return True
            
        except Exception as e:
            print(f"发送邮件失败: {e}")
            return False
