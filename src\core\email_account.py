#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱账户管理
"""

import json
import keyring
from typing import List, Dict, Optional
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class EmailAccount:
    """邮箱账户数据类"""
    id: str
    name: str
    email: str
    imap_server: str
    imap_port: int
    smtp_server: str
    smtp_port: int
    use_ssl: bool = True
    is_active: bool = True
    
    def to_dict(self) -> Dict:
        """转换为字典（不包含密码）"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'EmailAccount':
        """从字典创建实例"""
        return cls(**data)


class EmailAccountManager:
    """邮箱账户管理器"""
    
    def __init__(self, accounts_file: Path):
        """初始化账户管理器"""
        self.accounts_file = accounts_file
        self.accounts: List[EmailAccount] = []
        self.load_accounts()
    
    def load_accounts(self):
        """加载账户列表"""
        if self.accounts_file.exists():
            try:
                with open(self.accounts_file, 'r', encoding='utf-8') as f:
                    accounts_data = json.load(f)
                    self.accounts = [EmailAccount.from_dict(data) for data in accounts_data]
            except (json.JSONDecodeError, IOError):
                self.accounts = []
        else:
            self.accounts = []
    
    def save_accounts(self):
        """保存账户列表"""
        try:
            accounts_data = [account.to_dict() for account in self.accounts]
            with open(self.accounts_file, 'w', encoding='utf-8') as f:
                json.dump(accounts_data, f, indent=2, ensure_ascii=False)
        except IOError:
            pass
    
    def add_account(self, account: EmailAccount, password: str) -> bool:
        """添加账户"""
        # 检查邮箱是否已存在
        if any(acc.email == account.email for acc in self.accounts):
            return False
        
        # 保存密码到系统密钥环
        try:
            keyring.set_password("emailtool", account.email, password)
        except Exception:
            return False
        
        # 添加账户
        self.accounts.append(account)
        self.save_accounts()
        return True
    
    def remove_account(self, account_id: str) -> bool:
        """删除账户"""
        account = self.get_account(account_id)
        if not account:
            return False
        
        # 删除密码
        try:
            keyring.delete_password("emailtool", account.email)
        except Exception:
            pass
        
        # 删除账户
        self.accounts = [acc for acc in self.accounts if acc.id != account_id]
        self.save_accounts()
        return True
    
    def get_account(self, account_id: str) -> Optional[EmailAccount]:
        """获取账户"""
        for account in self.accounts:
            if account.id == account_id:
                return account
        return None
    
    def get_password(self, email: str) -> Optional[str]:
        """获取账户密码"""
        try:
            return keyring.get_password("emailtool", email)
        except Exception:
            return None
    
    def update_password(self, email: str, password: str) -> bool:
        """更新账户密码"""
        try:
            keyring.set_password("emailtool", email, password)
            return True
        except Exception:
            return False
    
    def get_all_accounts(self) -> List[EmailAccount]:
        """获取所有账户"""
        return self.accounts.copy()
    
    def get_active_accounts(self) -> List[EmailAccount]:
        """获取活跃账户"""
        return [acc for acc in self.accounts if acc.is_active]
