#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
应用程序样式定义
Material Design风格的现代化UI样式
"""


class AppStyles:
    """应用程序样式类"""
    
    # 颜色定义
    COLORS = {
        'primary': '#1976D2',
        'primary_dark': '#1565C0',
        'primary_light': '#42A5F5',
        'secondary': '#FFC107',
        'background': '#FAFAFA',
        'surface': '#FFFFFF',
        'error': '#F44336',
        'success': '#4CAF50',
        'warning': '#FF9800',
        'text_primary': '#212121',
        'text_secondary': '#757575',
        'divider': '#E0E0E0',
    }
    
    # 深色主题颜色
    DARK_COLORS = {
        'primary': '#2196F3',
        'primary_dark': '#1976D2',
        'primary_light': '#64B5F6',
        'secondary': '#FFC107',
        'background': '#121212',
        'surface': '#1E1E1E',
        'error': '#CF6679',
        'success': '#81C784',
        'warning': '#FFB74D',
        'text_primary': '#FFFFFF',
        'text_secondary': '#B0B0B0',
        'divider': '#2E2E2E',
    }
    
    @classmethod
    def get_main_window_style(cls, dark_mode=False) -> str:
        """获取主窗口样式"""
        colors = cls.DARK_COLORS if dark_mode else cls.COLORS
        
        return f"""
        QMainWindow {{
            background-color: {colors['background']};
            color: {colors['text_primary']};
        }}
        
        QMenuBar {{
            background-color: {colors['surface']};
            color: {colors['text_primary']};
            border-bottom: 1px solid {colors['divider']};
            padding: 4px;
        }}
        
        QMenuBar::item {{
            background-color: transparent;
            padding: 8px 12px;
            border-radius: 4px;
        }}
        
        QMenuBar::item:selected {{
            background-color: {colors['primary_light']};
        }}
        
        QStatusBar {{
            background-color: {colors['surface']};
            color: {colors['text_secondary']};
            border-top: 1px solid {colors['divider']};
        }}
        
        QToolBar {{
            background-color: {colors['surface']};
            border: none;
            spacing: 4px;
            padding: 4px;
        }}
        
        QToolButton {{
            background-color: transparent;
            border: none;
            padding: 8px;
            border-radius: 4px;
            min-width: 32px;
            min-height: 32px;
        }}
        
        QToolButton:hover {{
            background-color: {colors['primary_light']};
        }}
        
        QToolButton:pressed {{
            background-color: {colors['primary_dark']};
        }}
        """
    
    @classmethod
    def get_sidebar_style(cls, dark_mode=False) -> str:
        """获取侧边栏样式"""
        colors = cls.DARK_COLORS if dark_mode else cls.COLORS
        
        return f"""
        QListWidget {{
            background-color: {colors['surface']};
            border: none;
            outline: none;
            padding: 8px;
        }}
        
        QListWidget::item {{
            background-color: transparent;
            color: {colors['text_primary']};
            padding: 12px 16px;
            border-radius: 8px;
            margin: 2px 0px;
            font-size: 14px;
        }}
        
        QListWidget::item:hover {{
            background-color: {colors['primary_light']};
        }}
        
        QListWidget::item:selected {{
            background-color: {colors['primary']};
            color: white;
        }}
        """
    
    @classmethod
    def get_button_style(cls, dark_mode=False) -> str:
        """获取按钮样式"""
        colors = cls.DARK_COLORS if dark_mode else cls.COLORS
        
        return f"""
        QPushButton {{
            background-color: {colors['primary']};
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
        }}
        
        QPushButton:hover {{
            background-color: {colors['primary_dark']};
        }}
        
        QPushButton:pressed {{
            background-color: {colors['primary_light']};
        }}
        
        QPushButton:disabled {{
            background-color: {colors['divider']};
            color: {colors['text_secondary']};
        }}
        
        QPushButton.secondary {{
            background-color: transparent;
            color: {colors['primary']};
            border: 2px solid {colors['primary']};
        }}
        
        QPushButton.secondary:hover {{
            background-color: {colors['primary_light']};
            color: white;
        }}
        """
