#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱工具主程序入口
使用PyQt6开发的现代化邮箱客户端
"""

import sys
import os
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QIcon

# 添加src目录到Python路径
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

from ui.main_window import MainWindow
from core.config import AppConfig


def main():
    """主程序入口"""
    # 在PyQt6中，高DPI支持需要在创建QApplication之前设置
    QApplication.setHighDpiScaleFactorRoundingPolicy(Qt.HighDpiScaleFactorRoundingPolicy.PassThrough)

    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("邮箱工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("EmailTool")
    
    # 初始化配置
    config = AppConfig()
    
    # 创建主窗口
    main_window = MainWindow()
    main_window.show()
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
