#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮件查看器组件
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QPushButton, QTextEdit, QScrollArea, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap


class EmailViewerWidget(QWidget):
    """邮件查看器组件"""
    
    # 信号定义
    reply_requested = pyqtSignal(dict)  # 回复邮件信号
    forward_requested = pyqtSignal(dict)  # 转发邮件信号
    
    def __init__(self):
        """初始化邮件查看器"""
        super().__init__()
        self.current_email = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        
        # 创建邮件头部
        self.create_email_header(layout)
        
        # 创建操作按钮栏
        self.create_action_bar(layout)
        
        # 创建邮件内容区域
        self.create_content_area(layout)
        
        # 初始显示空状态
        self.show_empty_state()
    
    def create_email_header(self, parent_layout):
        """创建邮件头部"""
        # 头部容器
        header_frame = QFrame()
        header_frame.setFrameStyle(QFrame.Shape.Box)
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #f5f5f5;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        
        # 主题
        self.subject_label = QLabel("选择一封邮件查看")
        subject_font = QFont()
        subject_font.setPointSize(16)
        subject_font.setBold(True)
        self.subject_label.setFont(subject_font)
        header_layout.addWidget(self.subject_label)
        
        # 发件人信息
        sender_layout = QHBoxLayout()
        
        self.sender_label = QLabel("发件人: ")
        sender_layout.addWidget(self.sender_label)
        
        sender_layout.addStretch()
        
        self.time_label = QLabel("时间: ")
        sender_layout.addWidget(self.time_label)
        
        header_layout.addLayout(sender_layout)
        
        # 收件人信息
        self.recipient_label = QLabel("收件人: ")
        header_layout.addWidget(self.recipient_label)
        
        parent_layout.addWidget(header_frame)
    
    def create_action_bar(self, parent_layout):
        """创建操作按钮栏"""
        action_layout = QHBoxLayout()
        
        # 回复按钮
        self.reply_btn = QPushButton("回复")
        self.reply_btn.clicked.connect(self.reply_email)
        self.reply_btn.setEnabled(False)
        action_layout.addWidget(self.reply_btn)
        
        # 全部回复按钮
        self.reply_all_btn = QPushButton("全部回复")
        self.reply_all_btn.clicked.connect(self.reply_all_email)
        self.reply_all_btn.setEnabled(False)
        action_layout.addWidget(self.reply_all_btn)
        
        # 转发按钮
        self.forward_btn = QPushButton("转发")
        self.forward_btn.clicked.connect(self.forward_email)
        self.forward_btn.setEnabled(False)
        action_layout.addWidget(self.forward_btn)
        
        action_layout.addStretch()
        
        # 删除按钮
        self.delete_btn = QPushButton("删除")
        self.delete_btn.clicked.connect(self.delete_email)
        self.delete_btn.setEnabled(False)
        self.delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
            }
            QPushButton:hover {
                background-color: #d32f2f;
            }
        """)
        action_layout.addWidget(self.delete_btn)
        
        parent_layout.addLayout(action_layout)
    
    def create_content_area(self, parent_layout):
        """创建邮件内容区域"""
        # 内容文本框
        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        self.content_text.setMinimumHeight(200)
        
        parent_layout.addWidget(self.content_text)
    
    def show_empty_state(self):
        """显示空状态"""
        self.subject_label.setText("选择一封邮件查看")
        self.sender_label.setText("发件人: ")
        self.time_label.setText("时间: ")
        self.recipient_label.setText("收件人: ")
        self.content_text.setPlainText("请从左侧邮件列表中选择一封邮件查看详细内容。")
        
        # 禁用操作按钮
        self.reply_btn.setEnabled(False)
        self.reply_all_btn.setEnabled(False)
        self.forward_btn.setEnabled(False)
        self.delete_btn.setEnabled(False)
    
    def display_email(self, email):
        """显示邮件内容"""
        self.current_email = email
        
        # 更新头部信息
        self.subject_label.setText(email.get("subject", "无主题"))
        self.sender_label.setText(f"发件人: {email.get('sender', '未知')}")
        self.time_label.setText(f"时间: {email.get('time', '未知')}")
        self.recipient_label.setText(f"收件人: {email.get('recipient', '我')}")
        
        # 更新内容
        content = email.get("content", "邮件内容为空")
        self.content_text.setPlainText(content)
        
        # 启用操作按钮
        self.reply_btn.setEnabled(True)
        self.reply_all_btn.setEnabled(True)
        self.forward_btn.setEnabled(True)
        self.delete_btn.setEnabled(True)
    
    def reply_email(self):
        """回复邮件"""
        if self.current_email:
            self.reply_requested.emit(self.current_email)
    
    def reply_all_email(self):
        """全部回复邮件"""
        if self.current_email:
            # TODO: 实现全部回复逻辑
            self.reply_requested.emit(self.current_email)
    
    def forward_email(self):
        """转发邮件"""
        if self.current_email:
            self.forward_requested.emit(self.current_email)
    
    def delete_email(self):
        """删除邮件"""
        if self.current_email:
            # TODO: 实现删除邮件逻辑
            pass
    
    def clear_content(self):
        """清空内容"""
        self.show_empty_state()
        self.current_email = None
