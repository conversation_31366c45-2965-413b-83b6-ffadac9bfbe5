#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
邮箱账户配置对话框
"""

import uuid
from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QSpinBox, QCheckBox, QPushButton,
    QLabel, QComboBox, QGroupBox, QMessageBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont

from core.email_account import EmailAccount


class AccountDialog(QDialog):
    """邮箱账户配置对话框"""
    
    def __init__(self, parent=None):
        """初始化对话框"""
        super().__init__(parent)
        self.setWindowTitle("添加邮箱账户")
        self.setModal(True)
        self.setMinimumWidth(500)
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        
        # 创建基本信息组
        self.create_basic_info_group(layout)
        
        # 创建服务器配置组
        self.create_server_config_group(layout)
        
        # 创建按钮栏
        self.create_button_bar(layout)
        
        # 设置预设配置
        self.setup_presets()
    
    def create_basic_info_group(self, parent_layout):
        """创建基本信息组"""
        group = QGroupBox("基本信息")
        form_layout = QFormLayout(group)
        
        # 账户名称
        self.name_edit = QLineEdit()
        self.name_edit.setPlaceholderText("例如：工作邮箱")
        form_layout.addRow("账户名称:", self.name_edit)
        
        # 邮箱地址
        self.email_edit = QLineEdit()
        self.email_edit.setPlaceholderText("<EMAIL>")
        self.email_edit.textChanged.connect(self.on_email_changed)
        form_layout.addRow("邮箱地址:", self.email_edit)
        
        # 密码
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_edit.setPlaceholderText("邮箱密码或应用专用密码")
        form_layout.addRow("密码:", self.password_edit)
        
        parent_layout.addWidget(group)
    
    def create_server_config_group(self, parent_layout):
        """创建服务器配置组"""
        group = QGroupBox("服务器配置")
        form_layout = QFormLayout(group)
        
        # 邮箱提供商预设
        self.provider_combo = QComboBox()
        self.provider_combo.addItems([
            "自定义配置",
            "Gmail",
            "Outlook/Hotmail",
            "QQ邮箱",
            "163邮箱",
            "126邮箱",
            "新浪邮箱"
        ])
        self.provider_combo.currentTextChanged.connect(self.on_provider_changed)
        form_layout.addRow("邮箱提供商:", self.provider_combo)
        
        # IMAP服务器配置
        imap_layout = QHBoxLayout()
        self.imap_server_edit = QLineEdit()
        self.imap_server_edit.setPlaceholderText("imap.gmail.com")
        imap_layout.addWidget(self.imap_server_edit)
        
        self.imap_port_spin = QSpinBox()
        self.imap_port_spin.setRange(1, 65535)
        self.imap_port_spin.setValue(993)
        self.imap_port_spin.setMaximumWidth(80)
        imap_layout.addWidget(self.imap_port_spin)
        
        form_layout.addRow("IMAP服务器:", imap_layout)
        
        # SMTP服务器配置
        smtp_layout = QHBoxLayout()
        self.smtp_server_edit = QLineEdit()
        self.smtp_server_edit.setPlaceholderText("smtp.gmail.com")
        smtp_layout.addWidget(self.smtp_server_edit)
        
        self.smtp_port_spin = QSpinBox()
        self.smtp_port_spin.setRange(1, 65535)
        self.smtp_port_spin.setValue(587)
        self.smtp_port_spin.setMaximumWidth(80)
        smtp_layout.addWidget(self.smtp_port_spin)
        
        form_layout.addRow("SMTP服务器:", smtp_layout)
        
        # SSL/TLS选项
        self.ssl_checkbox = QCheckBox("使用SSL/TLS加密")
        self.ssl_checkbox.setChecked(True)
        form_layout.addRow("", self.ssl_checkbox)
        
        parent_layout.addWidget(group)
    
    def create_button_bar(self, parent_layout):
        """创建按钮栏"""
        button_layout = QHBoxLayout()
        
        # 测试连接按钮
        self.test_btn = QPushButton("测试连接")
        self.test_btn.clicked.connect(self.test_connection)
        button_layout.addWidget(self.test_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(self.accept_account)
        ok_btn.setDefault(True)
        button_layout.addWidget(ok_btn)
        
        parent_layout.addLayout(button_layout)
    
    def setup_presets(self):
        """设置预设配置"""
        self.presets = {
            "Gmail": {
                "imap_server": "imap.gmail.com",
                "imap_port": 993,
                "smtp_server": "smtp.gmail.com",
                "smtp_port": 587,
                "use_ssl": True
            },
            "Outlook/Hotmail": {
                "imap_server": "outlook.office365.com",
                "imap_port": 993,
                "smtp_server": "smtp-mail.outlook.com",
                "smtp_port": 587,
                "use_ssl": True
            },
            "QQ邮箱": {
                "imap_server": "imap.qq.com",
                "imap_port": 993,
                "smtp_server": "smtp.qq.com",
                "smtp_port": 587,
                "use_ssl": True
            },
            "163邮箱": {
                "imap_server": "imap.163.com",
                "imap_port": 993,
                "smtp_server": "smtp.163.com",
                "smtp_port": 587,
                "use_ssl": True
            },
            "126邮箱": {
                "imap_server": "imap.126.com",
                "imap_port": 993,
                "smtp_server": "smtp.126.com",
                "smtp_port": 587,
                "use_ssl": True
            },
            "新浪邮箱": {
                "imap_server": "imap.sina.com",
                "imap_port": 993,
                "smtp_server": "smtp.sina.com",
                "smtp_port": 587,
                "use_ssl": True
            }
        }
    
    def on_email_changed(self, email):
        """邮箱地址变化时自动设置账户名称"""
        if email and not self.name_edit.text():
            # 从邮箱地址提取用户名作为账户名称
            username = email.split('@')[0] if '@' in email else email
            self.name_edit.setText(username)
    
    def on_provider_changed(self, provider):
        """邮箱提供商变化时应用预设配置"""
        if provider in self.presets:
            preset = self.presets[provider]
            self.imap_server_edit.setText(preset["imap_server"])
            self.imap_port_spin.setValue(preset["imap_port"])
            self.smtp_server_edit.setText(preset["smtp_server"])
            self.smtp_port_spin.setValue(preset["smtp_port"])
            self.ssl_checkbox.setChecked(preset["use_ssl"])
    
    def test_connection(self):
        """测试邮箱连接"""
        # TODO: 实现邮箱连接测试
        QMessageBox.information(self, "测试连接", "连接测试功能待实现")
    
    def accept_account(self):
        """确认添加账户"""
        # 验证输入
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "错误", "请输入账户名称")
            return
        
        if not self.email_edit.text().strip():
            QMessageBox.warning(self, "错误", "请输入邮箱地址")
            return
        
        if not self.password_edit.text():
            QMessageBox.warning(self, "错误", "请输入密码")
            return
        
        if not self.imap_server_edit.text().strip():
            QMessageBox.warning(self, "错误", "请输入IMAP服务器")
            return
        
        if not self.smtp_server_edit.text().strip():
            QMessageBox.warning(self, "错误", "请输入SMTP服务器")
            return
        
        self.accept()
    
    def get_account_data(self):
        """获取账户数据"""
        account = EmailAccount(
            id=str(uuid.uuid4()),
            name=self.name_edit.text().strip(),
            email=self.email_edit.text().strip(),
            imap_server=self.imap_server_edit.text().strip(),
            imap_port=self.imap_port_spin.value(),
            smtp_server=self.smtp_server_edit.text().strip(),
            smtp_port=self.smtp_port_spin.value(),
            use_ssl=self.ssl_checkbox.isChecked(),
            is_active=True
        )
        
        password = self.password_edit.text()
        
        return account, password
