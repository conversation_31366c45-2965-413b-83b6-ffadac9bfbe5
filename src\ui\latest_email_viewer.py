#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最新邮件查看器组件
显示当前选中账号的最新一封邮件的详细内容
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
    QTextEdit, QFrame, QPushButton, QScrollArea
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QPixmap
from typing import Optional, Dict
from datetime import datetime
from core.email_account import EmailAccount


class LatestEmailViewerWidget(QWidget):
    """最新邮件查看器组件"""
    
    # 信号定义
    refresh_requested = pyqtSignal(EmailAccount)  # 刷新邮件请求信号
    
    def __init__(self):
        """初始化最新邮件查看器"""
        super().__init__()
        self.current_account: Optional[EmailAccount] = None
        self.current_email: Optional[Dict] = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 创建标题栏
        self.create_header(layout)
        
        # 创建内容区域
        self.create_content_area(layout)
        
        # 初始显示空状态
        self.show_empty_state()
    
    def create_header(self, parent_layout):
        """创建标题栏"""
        self.header_frame = QFrame()
        self.header_frame.setStyleSheet("""
            QFrame {
                background-color: #1976D2;
                color: white;
                padding: 12px;
                border-bottom: 1px solid #E0E0E0;
            }
        """)
        
        header_layout = QHBoxLayout(self.header_frame)
        header_layout.setContentsMargins(12, 12, 12, 12)
        
        # 标题
        self.title_label = QLabel("最新邮件")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        self.title_label.setFont(title_font)
        self.title_label.setStyleSheet("color: white;")
        
        header_layout.addWidget(self.title_label)
        header_layout.addStretch()
        
        # 刷新按钮
        self.refresh_button = QPushButton("刷新")
        self.refresh_button.setStyleSheet("""
            QPushButton {
                background-color: rgba(255, 255, 255, 0.2);
                color: white;
                border: 1px solid rgba(255, 255, 255, 0.3);
                padding: 6px 12px;
                border-radius: 4px;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: rgba(255, 255, 255, 0.3);
            }
            QPushButton:pressed {
                background-color: rgba(255, 255, 255, 0.1);
            }
        """)
        self.refresh_button.clicked.connect(self.on_refresh_clicked)
        header_layout.addWidget(self.refresh_button)
        
        parent_layout.addWidget(self.header_frame)
    
    def create_content_area(self, parent_layout):
        """创建内容区域"""
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: white;
            }
        """)
        
        # 内容容器
        self.content_widget = QWidget()
        self.content_layout = QVBoxLayout(self.content_widget)
        self.content_layout.setContentsMargins(20, 20, 20, 20)
        self.content_layout.setSpacing(16)
        
        scroll_area.setWidget(self.content_widget)
        parent_layout.addWidget(scroll_area)
    
    def show_empty_state(self):
        """显示空状态"""
        self.clear_content()
        
        # 空状态容器
        empty_widget = QWidget()
        empty_layout = QVBoxLayout(empty_widget)
        empty_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 空状态图标（使用文本代替）
        icon_label = QLabel("📧")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px; color: #BDBDBD; margin-bottom: 16px;")
        empty_layout.addWidget(icon_label)
        
        # 空状态文本
        text_label = QLabel("请选择一个邮箱账号\n查看最新邮件")
        text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_label.setStyleSheet("""
            QLabel {
                color: #757575;
                font-size: 14px;
                line-height: 1.5;
            }
        """)
        empty_layout.addWidget(text_label)
        
        self.content_layout.addWidget(empty_widget)
    
    def show_no_email_state(self, account: EmailAccount):
        """显示无邮件状态"""
        self.clear_content()
        
        # 无邮件状态容器
        no_email_widget = QWidget()
        no_email_layout = QVBoxLayout(no_email_widget)
        no_email_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
        
        # 图标
        icon_label = QLabel("📭")
        icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        icon_label.setStyleSheet("font-size: 48px; color: #BDBDBD; margin-bottom: 16px;")
        no_email_layout.addWidget(icon_label)
        
        # 文本
        text_label = QLabel(f"{account.name}\n暂无邮件")
        text_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        text_label.setStyleSheet("""
            QLabel {
                color: #757575;
                font-size: 14px;
                line-height: 1.5;
            }
        """)
        no_email_layout.addWidget(text_label)
        
        self.content_layout.addWidget(no_email_widget)
    
    def display_email(self, account: EmailAccount, email: Optional[Dict] = None):
        """显示邮件内容"""
        self.current_account = account
        self.current_email = email
        
        # 更新标题
        self.title_label.setText(f"{account.name} - 最新邮件")
        
        if not email:
            self.show_no_email_state(account)
            return
        
        self.clear_content()
        
        # 邮件头部信息
        self.create_email_header(email)
        
        # 邮件内容
        self.create_email_content(email)
    
    def create_email_header(self, email: Dict):
        """创建邮件头部信息"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #F5F5F5;
                border-radius: 8px;
                padding: 16px;
            }
        """)
        
        header_layout = QVBoxLayout(header_frame)
        header_layout.setSpacing(8)
        
        # 主题
        subject_label = QLabel(email.get("subject", "无主题"))
        subject_font = QFont()
        subject_font.setBold(True)
        subject_font.setPointSize(16)
        subject_label.setFont(subject_font)
        subject_label.setStyleSheet("color: #212121; margin-bottom: 8px;")
        subject_label.setWordWrap(True)
        header_layout.addWidget(subject_label)
        
        # 发件人
        sender_layout = QHBoxLayout()
        sender_label = QLabel("发件人:")
        sender_label.setStyleSheet("color: #757575; font-weight: bold; min-width: 60px;")
        sender_value = QLabel(email.get("sender", "未知"))
        sender_value.setStyleSheet("color: #212121;")
        sender_layout.addWidget(sender_label)
        sender_layout.addWidget(sender_value)
        sender_layout.addStretch()
        header_layout.addLayout(sender_layout)
        
        # 收件人
        recipient_layout = QHBoxLayout()
        recipient_label = QLabel("收件人:")
        recipient_label.setStyleSheet("color: #757575; font-weight: bold; min-width: 60px;")
        recipient_value = QLabel(email.get("recipient", self.current_account.email if self.current_account else "我"))
        recipient_value.setStyleSheet("color: #212121;")
        recipient_layout.addWidget(recipient_label)
        recipient_layout.addWidget(recipient_value)
        recipient_layout.addStretch()
        header_layout.addLayout(recipient_layout)
        
        # 时间
        time_layout = QHBoxLayout()
        time_label = QLabel("时间:")
        time_label.setStyleSheet("color: #757575; font-weight: bold; min-width: 60px;")
        time_value = QLabel(email.get("time", "未知"))
        time_value.setStyleSheet("color: #212121;")
        time_layout.addWidget(time_label)
        time_layout.addWidget(time_value)
        time_layout.addStretch()
        header_layout.addLayout(time_layout)
        
        self.content_layout.addWidget(header_frame)
    
    def create_email_content(self, email: Dict):
        """创建邮件内容区域"""
        content_frame = QFrame()
        content_frame.setStyleSheet("""
            QFrame {
                background-color: white;
                border: 1px solid #E0E0E0;
                border-radius: 8px;
                padding: 16px;
            }
        """)
        
        content_layout = QVBoxLayout(content_frame)
        
        # 内容标题
        content_title = QLabel("邮件正文:")
        content_title.setStyleSheet("color: #757575; font-weight: bold; margin-bottom: 8px;")
        content_layout.addWidget(content_title)
        
        # 邮件正文
        content_text = QTextEdit()
        content_text.setPlainText(email.get("content", "邮件内容为空"))
        content_text.setReadOnly(True)
        content_text.setMinimumHeight(200)
        content_text.setStyleSheet("""
            QTextEdit {
                border: none;
                background-color: transparent;
                color: #212121;
                font-size: 13px;
                line-height: 1.5;
            }
        """)
        content_layout.addWidget(content_text)
        
        self.content_layout.addWidget(content_frame)
    
    def clear_content(self):
        """清空内容区域"""
        while self.content_layout.count():
            child = self.content_layout.takeAt(0)
            if child.widget():
                child.widget().deleteLater()
    
    def on_refresh_clicked(self):
        """刷新按钮点击处理"""
        if self.current_account:
            self.refresh_requested.emit(self.current_account)
    
    def set_account(self, account: Optional[EmailAccount]):
        """设置当前账号"""
        if account:
            self.display_email(account)
        else:
            self.current_account = None
            self.current_email = None
            self.title_label.setText("最新邮件")
            self.show_empty_state()
