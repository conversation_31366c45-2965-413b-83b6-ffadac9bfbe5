#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号列表组件
显示用户已添加的所有邮箱账号列表
"""

from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QListWidget, 
    QListWidgetItem, QPushButton, QLabel, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon
from typing import List, Optional
from core.email_account import EmailAccount


class AccountListItem(QWidget):
    """账号列表项组件"""
    
    def __init__(self, account: EmailAccount, unread_count: int = 0):
        """初始化账号列表项"""
        super().__init__()
        self.account = account
        self.unread_count = unread_count
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(4)
        
        # 账号名称和邮箱地址
        name_layout = QHBoxLayout()
        
        # 账号名称
        self.name_label = QLabel(self.account.name)
        name_font = QFont()
        name_font.setBold(True)
        name_font.setPointSize(12)
        self.name_label.setFont(name_font)
        name_layout.addWidget(self.name_label)
        
        # 未读邮件数量
        if self.unread_count > 0:
            self.unread_label = QLabel(str(self.unread_count))
            self.unread_label.setStyleSheet("""
                QLabel {
                    background-color: #F44336;
                    color: white;
                    border-radius: 10px;
                    padding: 2px 6px;
                    font-size: 10px;
                    font-weight: bold;
                    min-width: 16px;
                    max-width: 32px;
                }
            """)
            self.unread_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            name_layout.addWidget(self.unread_label)
        
        name_layout.addStretch()
        layout.addLayout(name_layout)
        
        # 邮箱地址
        self.email_label = QLabel(self.account.email)
        email_font = QFont()
        email_font.setPointSize(10)
        self.email_label.setFont(email_font)
        self.email_label.setStyleSheet("color: #757575;")
        layout.addWidget(self.email_label)
        
        # 连接状态指示器
        status_layout = QHBoxLayout()
        self.status_indicator = QLabel("●")
        if self.account.is_active:
            self.status_indicator.setStyleSheet("color: #4CAF50; font-size: 8px;")
            self.status_indicator.setToolTip("账号已连接")
        else:
            self.status_indicator.setStyleSheet("color: #F44336; font-size: 8px;")
            self.status_indicator.setToolTip("账号未连接")
        
        status_text = QLabel("已连接" if self.account.is_active else "未连接")
        status_text.setStyleSheet("color: #757575; font-size: 9px;")
        
        status_layout.addWidget(self.status_indicator)
        status_layout.addWidget(status_text)
        status_layout.addStretch()
        layout.addLayout(status_layout)
    
    def update_unread_count(self, count: int):
        """更新未读邮件数量"""
        self.unread_count = count
        # 重新初始化UI以更新显示
        # 这里可以优化为只更新相关部分
        for i in reversed(range(self.layout().count())):
            self.layout().itemAt(i).widget().setParent(None)
        self.init_ui()


class AccountListWidget(QWidget):
    """账号列表组件"""
    
    # 信号定义
    account_selected = pyqtSignal(EmailAccount)  # 账号选中信号
    add_account_requested = pyqtSignal()  # 添加账号请求信号
    
    def __init__(self):
        """初始化账号列表"""
        super().__init__()
        self.accounts: List[EmailAccount] = []
        self.current_account: Optional[EmailAccount] = None
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)
        
        # 标题栏
        self.create_header(layout)
        
        # 账号列表
        self.create_account_list(layout)
        
        # 添加账号按钮
        self.create_add_button(layout)
    
    def create_header(self, parent_layout):
        """创建标题栏"""
        header_frame = QFrame()
        header_frame.setStyleSheet("""
            QFrame {
                background-color: #1976D2;
                color: white;
                padding: 12px;
            }
        """)
        
        header_layout = QHBoxLayout(header_frame)
        header_layout.setContentsMargins(12, 12, 12, 12)
        
        title_label = QLabel("邮箱账号")
        title_font = QFont()
        title_font.setBold(True)
        title_font.setPointSize(14)
        title_label.setFont(title_font)
        title_label.setStyleSheet("color: white;")
        
        header_layout.addWidget(title_label)
        header_layout.addStretch()
        
        parent_layout.addWidget(header_frame)
    
    def create_account_list(self, parent_layout):
        """创建账号列表"""
        self.account_list = QListWidget()
        self.account_list.setStyleSheet("""
            QListWidget {
                border: none;
                background-color: #FAFAFA;
                outline: none;
            }
            QListWidget::item {
                border-bottom: 1px solid #E0E0E0;
                padding: 0px;
                margin: 0px;
            }
            QListWidget::item:selected {
                background-color: #E3F2FD;
            }
            QListWidget::item:hover {
                background-color: #F5F5F5;
            }
        """)
        
        # 连接选择信号
        self.account_list.currentRowChanged.connect(self.on_account_selected)
        
        parent_layout.addWidget(self.account_list)
    
    def create_add_button(self, parent_layout):
        """创建添加账号按钮"""
        button_frame = QFrame()
        button_frame.setStyleSheet("background-color: #FAFAFA; padding: 8px;")
        
        button_layout = QHBoxLayout(button_frame)
        button_layout.setContentsMargins(8, 8, 8, 8)
        
        self.add_button = QPushButton("+ 添加邮箱账号")
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #1976D2;
                color: white;
                border: none;
                padding: 10px 16px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 500;
            }
            QPushButton:hover {
                background-color: #1565C0;
            }
            QPushButton:pressed {
                background-color: #0D47A1;
            }
        """)
        
        self.add_button.clicked.connect(self.add_account_requested.emit)
        
        button_layout.addWidget(self.add_button)
        parent_layout.addWidget(button_frame)
    
    def load_accounts(self, accounts: List[EmailAccount]):
        """加载账号列表"""
        self.accounts = accounts
        self.refresh_list()
    
    def refresh_list(self):
        """刷新账号列表显示"""
        self.account_list.clear()
        
        if not self.accounts:
            # 显示空状态
            empty_item = QListWidgetItem()
            empty_widget = QLabel("暂无邮箱账号\n点击下方按钮添加")
            empty_widget.setAlignment(Qt.AlignmentFlag.AlignCenter)
            empty_widget.setStyleSheet("""
                QLabel {
                    color: #757575;
                    padding: 40px 20px;
                    font-size: 12px;
                }
            """)
            empty_item.setSizeHint(empty_widget.sizeHint())
            self.account_list.addItem(empty_item)
            self.account_list.setItemWidget(empty_item, empty_widget)
            return
        
        # 添加账号项
        for account in self.accounts:
            # TODO: 这里应该从邮件管理器获取实际的未读邮件数量
            unread_count = 0  # 临时设置为0
            
            item = QListWidgetItem()
            account_widget = AccountListItem(account, unread_count)
            
            item.setSizeHint(account_widget.sizeHint())
            self.account_list.addItem(item)
            self.account_list.setItemWidget(item, account_widget)
        
        # 默认选中第一个账号
        if self.accounts:
            self.account_list.setCurrentRow(0)
    
    def on_account_selected(self, row: int):
        """账号选择变化处理"""
        if 0 <= row < len(self.accounts):
            self.current_account = self.accounts[row]
            self.account_selected.emit(self.current_account)
    
    def get_current_account(self) -> Optional[EmailAccount]:
        """获取当前选中的账号"""
        return self.current_account
    
    def update_account_unread_count(self, account_id: str, count: int):
        """更新指定账号的未读邮件数量"""
        for i in range(self.account_list.count()):
            item = self.account_list.item(i)
            widget = self.account_list.itemWidget(item)
            if isinstance(widget, AccountListItem) and widget.account.id == account_id:
                widget.update_unread_count(count)
                break
